<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>جميل علقم - السيرة التدريبية</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Cairo:wght@300;400;600&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      /* أنماط CSS من صفحة index مع تعديلات */
      @font-face {
        font-family: "Bahij TheSansArabic";
        src: url("fonts/Bahij_TheSansArabic-Plain.woff2") format("woff2");
        font-weight: normal;
        font-style: normal;
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Tajawal", "Cairo", sans-serif;
        background-color: #f5f5f5;
        color: #333;
        line-height: 1.7;
      }

      h1,
      h2,
      h3 {
        font-family: "Bahij TheSansArabic", "Tajawal", sans-serif;
        font-weight: 700;
      }

      /* شريط التنقل */
      .navbar {
        background-color: whitesmoke;
        padding: 1rem 2rem;
        position: sticky;
        top: 0;
        z-index: 1000;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .logo {
        color: #0760dc;
        font-family: "Bahij TheSansArabic", sans-serif;
        font-size: 1.5rem;
        font-weight: bold;
      }

      .nav-links {
        display: flex;
        list-style: none;
      }

      .nav-links li {
        margin-left: 2rem;
      }

      .nav-links a {
        color: black;
        text-decoration: none;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }

      .nav-links a i {
        margin-left: 0.5rem;
        font-size: 0.9rem;
      }

      .nav-links a:hover {
        color: #3498db;
        transform: translateY(-2px);
      }

      /* قسم العنوان الرئيسي */
      .main-title {
        background-color: #0760dc;
        color: white;
        padding: 3rem 2rem;
        text-align: center;
      }

      .main-title h1 {
        font-size: 2.5rem;
        margin-bottom: 1.5rem;
      }

      /* أزرار التبويب */
      .tab-buttons {
        display: flex;
        justify-content: center;
        margin: 2rem auto;
        max-width: 800px;
      }

      .tab-btn {
        background-color: white;
        border: none;
        padding: 1rem 2rem;
        margin: 0 0.5rem;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
      }

      .tab-btn i {
        margin-left: 0.5rem;
      }

      .tab-btn.active {
        background-color: #0760dc;
        color: white;
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .tab-btn:hover:not(.active) {
        background-color: #e0e0e0;
      }

      /* محتوى التبويبات */
      .tab-content {
        display: none;
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      }

      .tab-content.active {
        display: block;
        animation: fadeIn 0.5s ease;
      }

      /* قائمة الدورات */
      .course-list {
        list-style: none;
      }

      .course-item {
        padding: 1.5rem;
        margin-bottom: 1rem;
        border-bottom: 1px solid #eee;
        transition: all 0.3s ease;
        background-color: floralwhite;
        font-weight: bold;
      }

      .course-item:hover {
        background-color: antiquewhite;
        transform: translateX(10px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-radius: 5px;
      }

      .course-title {
        font-size: 1.2rem;
        color: #0760dc;
        margin-bottom: 0.5rem;
        font-weight: bold;
      }

      .course-details {
        display: flex;
        justify-content: space-between;
        color: #555;
      }

      /* الفوتر */
      footer {
        background-color: #0760dc;
        color: white;
        text-align: center;
        padding: 2rem;
        margin-top: 3rem;
      }

      .footer-content {
        max-width: 600px;
        margin: 0 auto;
      }

      .footer-name {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        font-family: "Bahij TheSansArabic", sans-serif;
        font-weight: bold;
      }

      .footer-quote {
        margin-bottom: 1.5rem;
        line-height: 1.8;
      }

      .copyright {
        font-size: 0.9rem;
        color: #bdc3c7;
      }

      /* تأثيرات الحركة */
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* زر العودة للأعلى */
      .back-to-top {
        position: fixed;
        bottom: 30px;
        right: 30px;
        background-color: #0760dc;
        color: white;
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(7, 96, 220, 0.3);
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        z-index: 1001;
      }

      .back-to-top.show {
        opacity: 1;
        visibility: visible;
      }

      .back-to-top:hover {
        background-color: #3498db;
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(7, 96, 220, 0.4);
      }

      /* التجاوب */
      @media (max-width: 768px) {
        .navbar {
          flex-direction: column;
          padding: 1rem;
          position: fixed;
          width: 100%;
          top: 0;
          transform: translateY(-100%);
          transition: transform 0.3s ease;
        }

        .navbar.show {
          transform: translateY(0);
        }

        .nav-links {
          margin-top: 1rem;
          flex-wrap: wrap;
          justify-content: center;
        }

        .nav-links li {
          margin: 0.5rem;
        }

        .tab-buttons {
          flex-direction: column;
        }

        .tab-btn {
          margin: 0.5rem 0;
        }

        /* إضافة مساحة علوية للمحتوى في الشاشات الصغيرة */
        .main-title {
          margin-top: 120px;
        }
      }

      .photo-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
      }

      @media (max-width: 768px) {
        .photos-grid {
          grid-template-columns: 1fr;
        }
      }

      /* نافذة العرض الكامل */
      .modal {
        display: none;
        position: fixed;
        z-index: 2000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        overflow: auto;
        animation: fadeIn 0.3s;
      }

      .modal-content {
        margin: auto;
        display: block;
        max-width: 90%;
        max-height: 90%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .close-btn {
        position: absolute;
        top: 20px;
        right: 30px;
        color: white;
        font-size: 35px;
        font-weight: bold;
        cursor: pointer;
        transition: 0.3s;
      }

      .close-btn:hover {
        color: #3498db;
      }

      .modal-caption {
        margin: auto;
        display: block;
        width: 80%;
        text-align: center;
        color: white;
        padding: 10px 0;
        position: absolute;
        bottom: 20px;
        left: 10%;
      }

      .publication-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
      }

      @media (max-width: 768px) {
        .publications-grid {
          grid-template-columns: 1fr;
        }
      }
      /* إزالة cursor: pointer من كل البطاقة */
      .photo-card,
      .publication-card {
        cursor: default; /* المؤشر العادي */
      }

      /* إضافة cursor: pointer للصور فقط */
      .photo-card img,
      .publication-card img {
        cursor: pointer; /* مؤشر اليد */
      }

      /* التأكيد على أن المؤشر يتغير فقط عند الصورة */
      .photo-card:hover,
      .publication-card:hover {
        cursor: default; /* يبقى المؤشر عادي حتى عند hover على البطاقة */
      }
    </style>
  </head>
  <body>
    <!-- شريط التنقل -->
    <nav class="navbar">
      <div class="logo">م. جميل علقم</div>
      <ul class="nav-links">
        <li>
          <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
        </li>
        <li>
          <a href="cv.html" style="color: #0760dc"
            ><i class="fas fa-chalkboard-teacher"></i> السيرة التدريبية</a
          >
        </li>
        <li>
          <a href="services.html"
            ><i class="fas fa-handshake"></i> الخدمات والدورات التدريبية</a
          >
        </li>
        <li>
          <a href="certificates.html"
            ><i class="fas fa-certificate"></i> الشهادات</a
          >
        </li>
        <li>
          <a href="contact.html"><i class="fas fa-envelope"></i> التواصل</a>
        </li>
      </ul>
    </nav>

    <!-- العنوان الرئيسي -->
    <section class="main-title">
      <h1>السيرة التدريبية</h1>
    </section>

    <!-- أزرار التبويب -->
    <div class="tab-buttons">
      <button class="tab-btn active" onclick="openTab('courses')">
        <i class="fas fa-graduation-cap"></i>
        الدورات التي قدمتها
      </button>
      <button class="tab-btn" onclick="openTab('photos')">
        <i class="fas fa-images"></i>
        صور من الدورات
      </button>
      <button class="tab-btn" onclick="openTab('publications')">
        <i class="fas fa-newspaper"></i>
        منشورات الدورات
      </button>
    </div>

    <!-- محتوى التبويب الأول -->
    <div id="courses" class="tab-content active">
      <h2 style="color: #0760dc; margin-bottom: 1.5rem; text-align: center">
        الدورات التدريبية التي قدمتها
      </h2>
      <ul class="course-list">
        <li class="course-item">
          <div class="course-title">مهارات القراءة السريعة</div>
          <div class="course-details">
            <span>12 ساعة تدريبية</span>
            <span>12 نوفمبر 2019</span>
          </div>
        </li>
        <li class="course-item">
          <div class="course-title">مهارات القراءة السريعة</div>
          <div class="course-details">
            <span>12 ساعة تدريبية</span>
            <span>11 يناير 2020</span>
          </div>
        </li>
        <li class="course-item">
          <div class="course-title">
            إعداد الجزء الإلكتروني من الحقيبة التدريبية
          </div>
          <div class="course-details">
            <span>24 ساعة تدريبية</span>
            <span>1 مارس 2020</span>
          </div>
        </li>
        <li class="course-item">
          <div class="course-title">مهارات القراءة السريعة</div>
          <div class="course-details">
            <span>12 ساعة تدريبية</span>
            <span>3 يناير 2021</span>
          </div>
        </li>
        <li class="course-item">
          <div class="course-title">مهارات القراءة السريعة</div>
          <div class="course-details">
            <span>12 ساعة تدريبية</span>
            <span>31 أغسطس 2021</span>
          </div>
        </li>
      </ul>
    </div>

    <!-- محتوى التبويب الثاني 
    <div id="photos" class="tab-content">
      <h2 style="color: #0760dc; margin-bottom: 1.5rem; text-align: center">
        صور من الدورات التدريبية
      </h2>
      <p style="text-align: center; color: #555">
        سيتم عرض الصور هنا عند الضغط على زر "صور من الدورات"
      </p>
    </div>-->

    <!-- استبدال محتوى تبويب صور من الدورات بهذا الكود -->
    <div id="photos" class="tab-content">
      <h2 style="color: #0760dc; margin-bottom: 1.5rem; text-align: center">
        صور من الدورات التدريبية
      </h2>

      <div
        class="photos-grid"
        style="
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 2rem;
          padding: 1rem;
        "
      >
        <!-- الصورة 1 -->
        <div
          class="photo-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          "
        >
          <img
            src="images/d1.jpg"
            alt="أول دورة في القراءة السريعة"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              أول دورة في القراءة السريعة
            </h3>
            <p style="color: #555; font-weight: bold">صورة جماعية للمتدربين</p>
          </div>
        </div>

        <!-- الصورة 2 -->
        <div
          class="photo-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          "
        >
          <img
            src="images/d2.jpg"
            alt="أول دورة في القراءة السريعة"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              أول دورة في القراءة السريعة
            </h3>
            <p style="color: #555; font-weight: bold">
              حصة تفاعلية مع المتدربين
            </p>
          </div>
        </div>

        <!-- الصورة 3 -->
        <div
          class="photo-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          "
        >
          <img
            src="images/d3.jpg"
            alt="ورشة إعداد الحقيبة التدريبية"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              ورشة إعداد الجزء الإلكتروني من الحقيبة التدريبية
            </h3>
            <p style="color: #555; font-weight: bold">
              حصة تفاعلية مع المتدربين
            </p>
          </div>
        </div>

        <!-- الصورة 4 -->
        <div
          class="photo-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          "
        >
          <img
            src="images/d4.png"
            alt="ثاني دورة في القراءة السريعة"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              ثاني دورة في القراءة السريعة
            </h3>
            <p style="color: #555; font-weight: bold">
              حصة تفاعلية مع المتدربين
            </p>
          </div>
        </div>

        <!-- الصورة 5 -->
        <div
          class="photo-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          "
        >
          <img
            src="images/d5.jpg"
            alt="ثاني دورة في القراءة السريعة"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              ثاني دورة في القراءة السريعة
            </h3>
            <p style="color: #555; font-weight: bold">صورة جماعية للمتدربين</p>
          </div>
        </div>

        <!-- الصورة 6 -->
        <div
          class="photo-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          "
        >
          <img
            src="images/d6.png"
            alt="ثاني دورة في القراءة السريعة"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              ثاني دورة في القراءة السريعة
            </h3>
            <p style="color: #555; font-weight: bold">
              تسليم الشهادات للمتدربين
            </p>
          </div>
        </div>

        <!-- الصورة 7 -->
        <div
          class="photo-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          "
        >
          <img
            src="images/d7.png"
            alt="دورة إعداد الحقيبة التدريبية"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              دورة إعداد الجزء الإلكتروني من الحقيبة التدريبية
            </h3>
            <p style="color: #555; font-weight: bold">صورة جماعية للمتدربين</p>
          </div>
        </div>

        <!-- الصورة 8 -->
        <div
          class="photo-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          "
        >
          <img
            src="images/d8.png"
            alt="ثالث دورة في القراءة السريعة"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              ثالث دورة في القراءة السريعة
            </h3>
            <p style="color: #555; font-weight: bold">صورة جماعية للمتدربين</p>
          </div>
        </div>

        <!-- الصورة 9 -->
        <div
          class="photo-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          "
        >
          <img
            src="images/d9.png"
            alt="ثالث دورة في القراءة السريعة"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              ثالث دورة في القراءة السريعة
            </h3>
            <p style="color: #555; font-weight: bold">
              تسليم الشهادات للمتدربين
            </p>
          </div>
        </div>

        <!-- الصورة 10 -->
        <div
          class="photo-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          "
        >
          <img
            src="images/d10.jpg"
            alt="رابع دورة في القراءة السريعة"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              رابع دورة في القراءة السريعة
            </h3>
            <p style="color: #555; font-weight: bold">صورة جماعية للمتدربين</p>
          </div>
        </div>
      </div>
    </div>

    <div id="imageModal" class="modal">
      <span class="close-btn">&times;</span>
      <img class="modal-content" id="expandedImg" />
      <div id="imgCaption" class="modal-caption"></div>
    </div>

    <!-- محتوى التبويب الثالث 
    <div id="publications" class="tab-content">
      <h2 style="color: #0760dc; margin-bottom: 1.5rem; text-align: center">
        منشورات الدورات التدريبية
      </h2>
      <p style="text-align: center; color: #555">
        سيتم عرض المنشورات هنا عند الضغط على زر "منشورات الدورات"
      </p>
    </div>-->

    <!-- استبدال محتوى تبويب منشورات الدورات بهذا الكود -->
    <div
      id="publications"
      class="tab-content"
      style="background-color: #0760dc"
    >
      <h2 style="color: white; margin-bottom: 1.5rem; text-align: center">
        منشورات الدورات التدريبية
      </h2>

      <div
        class="publications-grid"
        style="
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 2rem;
          padding: 1rem;
        "
      >
        <!-- المنشور 1 -->
        <div
          class="publication-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
          "
        >
          <img
            src="images/ad1.jpg"
            alt="منشور دورة القراءة السريعة"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem; border-top: 3px solid #333">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              منشور دورة القراءة السريعة
            </h3>
            <p style="color: #555; font-weight: bold">مركز توب كوالتي</p>
          </div>
        </div>

        <!-- المنشور 2 -->
        <div
          class="publication-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
          "
        >
          <img
            src="images/ad2.png"
            alt="منشور دورة إعداد الجزء الإلكتروني من الحقيبة التدريبية"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem; border-top: 3px solid #333">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              منشور دورة إعداد الجزء الإلكتروني من الحقيبة التدريبية
            </h3>
            <p style="color: #555; font-weight: bold">مركز توب كوالتي</p>
          </div>
        </div>

        <!-- المنشور 3 -->
        <div
          class="publication-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
          "
        >
          <img
            src="images/ad3.png"
            alt="منشور دورة تصميم قواعد البيانات باستخدام الأكسس"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem; border-top: 3px solid #333">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              منشور دورة تصميم قواعد البيانات باستخدام الأكسس
            </h3>
            <p style="color: #555; font-weight: bold">منصة فوادر التعليمية</p>
          </div>
        </div>

        <!-- المنشور 4 -->
        <div
          class="publication-card"
          style="
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
          "
        >
          <img
            src="images/ad4.png"
            alt="منشور دورة الإكسل الشاملة"
            style="width: 100%; height: 200px; object-fit: cover"
          />
          <div style="padding: 1.5rem; border-top: 3px solid #333">
            <h3 style="color: #0760dc; margin-bottom: 0.5rem">
              منشور دورة الإكسل الشاملة
            </h3>
            <p style="color: #555; font-weight: bold">منصة فوادر التعليمية</p>
          </div>
        </div>
      </div>
    </div>

    <!-- نفس نافذة العرض الكامل من السابق -->
    <div id="imageModal" class="modal">
      <span class="close-btn">&times;</span>
      <img class="modal-content" id="expandedImg" />
      <div id="imgCaption" class="modal-caption"></div>
    </div>

    <!-- زر العودة للأعلى -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()">
      <i class="fas fa-arrow-up"></i>
    </button>

    <!-- الفوتر -->
    <footer>
      <div class="footer-content">
        <div class="footer-name">جميل علقم</div>
        <p class="footer-quote">خبرة إدارية ومهارات تدريبية</p>
        <div class="copyright">
          © <span id="year"></span> جميل علقم جميع الحقوق محفوظة
        </div>
      </div>
    </footer>

    <!-- الجافاسكريبت -->
    <script src="js/image-fallback.js"></script>
    <script>
      // سنة حقوق النشر تلقائية
      document.getElementById("year").textContent = new Date().getFullYear();

      // التحكم في الهيدر المتحرك للشاشات الصغيرة
      let lastScrollTop = 0;
      const navbar = document.querySelector(".navbar");
      const backToTopBtn = document.getElementById("backToTop");

      window.addEventListener("scroll", function () {
        const scrollTop =
          window.pageYOffset || document.documentElement.scrollTop;

        // التحكم في الهيدر للشاشات الصغيرة فقط
        if (window.innerWidth <= 768) {
          if (scrollTop > lastScrollTop && scrollTop > 100) {
            // النزول للأسفل - إخفاء الهيدر
            navbar.classList.remove("show");
          } else {
            // الصعود للأعلى - إظهار الهيدر
            navbar.classList.add("show");
          }
        } else {
          // للشاشات الكبيرة - إظهار الهيدر دائماً
          navbar.classList.add("show");
        }

        // التحكم في زر العودة للأعلى
        if (scrollTop > 300) {
          backToTopBtn.classList.add("show");
        } else {
          backToTopBtn.classList.remove("show");
        }

        lastScrollTop = scrollTop;
      });

      // وظيفة العودة للأعلى
      function scrollToTop() {
        window.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      }

      // إظهار الهيدر عند تحميل الصفحة
      window.addEventListener("load", function () {
        navbar.classList.add("show");
      });

      // وظيفة تبديل التبويبات
      function openTab(tabId) {
        // إخفاء جميع المحتويات
        const tabContents = document.querySelectorAll(".tab-content");
        tabContents.forEach((content) => {
          content.classList.remove("active");
        });

        // إلغاء تنشيط جميع الأزرار
        const tabBtns = document.querySelectorAll(".tab-btn");
        tabBtns.forEach((btn) => {
          btn.classList.remove("active");
        });

        // إظهار المحتوى المحدد وتنشيط الزر
        document.getElementById(tabId).classList.add("active");
        event.currentTarget.classList.add("active");
      }
    </script>
    <script>
      // دالة لعرض الصورة بحجم كامل
      function expandImage(img) {
        const modal = document.getElementById("imageModal");
        const modalImg = document.getElementById("expandedImg");
        const captionText = document.getElementById("imgCaption");

        modal.style.display = "block";
        modalImg.src = img.src;
        captionText.innerHTML = img.alt;

        // إغلاق عند النقر على الزر
        document.querySelector(".close-btn").onclick = function () {
          modal.style.display = "none";
        };

        // إغلاق عند النقر خارج الصورة
        modal.onclick = function (event) {
          if (event.target === modal) {
            modal.style.display = "none";
          }
        };
      }

      // إضافة حدث النقر لكل صور البطاقات
      document.addEventListener("DOMContentLoaded", function () {
        const photoCards = document.querySelectorAll(".photo-card img");

        photoCards.forEach((img) => {
          img.onclick = function () {
            expandImage(this);
          };

          // إضافة مؤشر يد عند التمرير
          img.style.cursor = "pointer";
        });
      });
    </script>

    <script>
      // استخدام نفس دالة expandImage من السابق
      // إضافة حدث النقر لمنشورات الدورات
      document.addEventListener("DOMContentLoaded", function () {
        const publicationCards = document.querySelectorAll(
          ".publication-card img"
        );

        publicationCards.forEach((img) => {
          img.onclick = function () {
            expandImage(this);
          };
        });
      });
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // تغيير الحدث من البطاقة إلى الصورة فقط
        const photos = document.querySelectorAll(
          ".photo-card img, .publication-card img"
        );

        photos.forEach((img) => {
          img.onclick = function () {
            expandImage(this);
          };

          // المؤشر سيكون يداً فقط على الصورة
          img.style.cursor = "pointer";
        });

        // إزالة أي مؤشر يد من باقي أجزاء البطاقة
        const cards = document.querySelectorAll(
          ".photo-card, .publication-card"
        );
        cards.forEach((card) => {
          card.style.cursor = "default";
        });
      });
    </script>
  </body>
</html>
