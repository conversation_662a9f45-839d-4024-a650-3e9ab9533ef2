<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التواصل - جميل علقم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8f9fa;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        input:focus, textarea:focus {
            outline: none;
            border-color: #3498db;
        }
        textarea {
            height: 120px;
            resize: vertical;
        }
        .btn {
            background: #3498db;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .message {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إرسال رسالة عبر البريد الإلكتروني</h1>
        
        <form id="contactForm">
            <div class="form-group">
                <label for="name">اسمك</label>
                <input type="text" id="name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="email">بريدك الإلكتروني</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="subject">الموضوع (اختياري)</label>
                <input type="text" id="subject" name="subject">
            </div>
            
            <div class="form-group">
                <label for="message">نص الرسالة</label>
                <textarea id="message" name="message" required></textarea>
            </div>
            
            <button type="submit" class="btn" id="submitBtn">إرسال الرسالة</button>
        </form>
        
        <div id="result"></div>
        
        <div class="message info">
            <strong>ملاحظة:</strong> سيتم فتح تطبيق البريد الإلكتروني الافتراضي لديك لإرسال الرسالة.
        </div>
    </div>

    <script>
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const subject = document.getElementById('subject').value || 'رسالة من موقع جميل علقم';
            const message = document.getElementById('message').value;
            
            // إنشاء رابط mailto
            const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(`
الاسم: ${name}
البريد الإلكتروني: ${email}

الرسالة:
${message}
            `)}`;
            
            // فتح تطبيق البريد الإلكتروني
            window.location.href = mailtoLink;
            
            // عرض رسالة نجاح
            document.getElementById('result').innerHTML = `
                <div class="message success">
                    ✅ تم فتح تطبيق البريد الإلكتروني. يرجى إرسال الرسالة من هناك.
                </div>
            `;
        });
    </script>
</body>
</html>
